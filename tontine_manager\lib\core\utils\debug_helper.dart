import 'package:flutter/foundation.dart';
import 'package:tontine_manager/core/database/database_helper.dart';
import 'package:tontine_manager/features/auth/services/auth_service.dart';
import 'package:tontine_manager/shared/models/user.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';

/// Classe utilitaire pour diagnostiquer les problèmes d'authentification
class DebugHelper {
  static const _uuid = Uuid();
  static final AuthService _authService = AuthService();

  /// Diagnostiquer les problèmes de création de compte
  static Future<Map<String, dynamic>> diagnoseRegistration({
    required String phoneNumber,
    required String fullName,
    required String pin,
  }) async {
    final diagnosis = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'input_data': {
        'phoneNumber': phoneNumber,
        'fullName': fullName,
        'pin': '****', // Masqué pour sécurité
        'pin_length': pin.length,
      },
      'checks': <String, dynamic>{},
      'errors': <String>[],
      'warnings': <String>[],
      'success': false,
    };

    try {
      // 1. Vérifier la validité des données d'entrée
      diagnosis['checks']['input_validation'] = _validateInputData(
        phoneNumber, fullName, pin, diagnosis['errors'], diagnosis['warnings']
      );

      // 2. Vérifier la base de données
      diagnosis['checks']['database_status'] = await _checkDatabaseStatus(
        diagnosis['errors']
      );

      // 3. Vérifier si l'utilisateur existe déjà
      diagnosis['checks']['user_exists'] = await _checkUserExists(
        phoneNumber, diagnosis['errors']
      );

      // 4. Tester la création d'utilisateur
      if (diagnosis['checks']['input_validation'] && 
          diagnosis['checks']['database_status'] && 
          !diagnosis['checks']['user_exists']) {
        
        diagnosis['checks']['user_creation'] = await _testUserCreation(
          phoneNumber, fullName, pin, diagnosis['errors']
        );
        
        diagnosis['success'] = diagnosis['checks']['user_creation'];
      }

      // 5. Vérifier les permissions et l'environnement
      diagnosis['checks']['environment'] = await _checkEnvironment(
        diagnosis['warnings']
      );

    } catch (e) {
      diagnosis['errors'].add('Erreur critique lors du diagnostic: ${e.toString()}');
      diagnosis['checks']['critical_error'] = true;
    }

    return diagnosis;
  }

  /// Valider les données d'entrée
  static bool _validateInputData(
    String phoneNumber, 
    String fullName, 
    String pin,
    List<String> errors,
    List<String> warnings
  ) {
    bool isValid = true;

    // Validation du numéro de téléphone
    if (phoneNumber.isEmpty) {
      errors.add('Numéro de téléphone vide');
      isValid = false;
    } else {
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
      if (cleanNumber.length < 9) {
        errors.add('Numéro de téléphone trop court: ${cleanNumber.length} chiffres');
        isValid = false;
      } else if (cleanNumber.length > 15) {
        warnings.add('Numéro de téléphone très long: ${cleanNumber.length} chiffres');
      }
    }

    // Validation du nom complet
    if (fullName.isEmpty) {
      errors.add('Nom complet vide');
      isValid = false;
    } else if (fullName.length < 2) {
      errors.add('Nom complet trop court: ${fullName.length} caractères');
      isValid = false;
    }

    // Validation du PIN
    if (pin.isEmpty) {
      errors.add('Code PIN vide');
      isValid = false;
    } else if (pin.length != 4) {
      errors.add('Code PIN doit contenir 4 chiffres, reçu: ${pin.length}');
      isValid = false;
    } else if (!RegExp(r'^\d{4}$').hasMatch(pin)) {
      errors.add('Code PIN doit contenir uniquement des chiffres');
      isValid = false;
    }

    return isValid;
  }

  /// Vérifier le statut de la base de données
  static Future<bool> _checkDatabaseStatus(List<String> errors) async {
    try {
      final db = await DatabaseHelper.instance.database;
      
      // Vérifier que la table users existe
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='users'"
      );
      
      if (tables.isEmpty) {
        errors.add('Table users n\'existe pas dans la base de données');
        return false;
      }

      // Vérifier la structure de la table
      final columns = await db.rawQuery("PRAGMA table_info(users)");
      final expectedColumns = ['id', 'phone_number', 'full_name', 'pin_hash', 'created_at'];
      
      for (final expectedCol in expectedColumns) {
        final hasColumn = columns.any((col) => col['name'] == expectedCol);
        if (!hasColumn) {
          errors.add('Colonne manquante dans la table users: $expectedCol');
          return false;
        }
      }

      return true;
    } catch (e) {
      errors.add('Erreur d\'accès à la base de données: ${e.toString()}');
      return false;
    }
  }

  /// Vérifier si l'utilisateur existe déjà
  static Future<bool> _checkUserExists(String phoneNumber, List<String> errors) async {
    try {
      final existingUser = await _authService.getUserByPhone(phoneNumber);
      return existingUser != null;
    } catch (e) {
      errors.add('Erreur lors de la vérification d\'existence: ${e.toString()}');
      return false;
    }
  }

  /// Tester la création d'utilisateur
  static Future<bool> _testUserCreation(
    String phoneNumber, 
    String fullName, 
    String pin,
    List<String> errors
  ) async {
    try {
      // Créer un utilisateur de test
      final testUser = User(
        id: _uuid.v4(),
        phoneNumber: phoneNumber,
        fullName: fullName,
        pinHash: _hashPin(pin),
        createdAt: DateTime.now(),
      );

      // Tester la sérialisation
      final userMap = testUser.toMap();
      if (userMap.isEmpty) {
        errors.add('Erreur de sérialisation de l\'utilisateur');
        return false;
      }

      // Tester la création en base
      final success = await _authService.createUser(testUser);
      if (!success) {
        errors.add('Échec de la création en base de données');
        return false;
      }

      // Vérifier que l'utilisateur a été créé
      final createdUser = await _authService.getUserById(testUser.id);
      if (createdUser == null) {
        errors.add('Utilisateur non trouvé après création');
        return false;
      }

      return true;
    } catch (e) {
      errors.add('Erreur lors du test de création: ${e.toString()}');
      return false;
    }
  }

  /// Vérifier l'environnement
  static Future<Map<String, dynamic>> _checkEnvironment(List<String> warnings) async {
    final env = <String, dynamic>{};

    try {
      env['platform'] = defaultTargetPlatform.name;
      env['debug_mode'] = kDebugMode;
      env['profile_mode'] = kProfileMode;
      env['release_mode'] = kReleaseMode;

      // Vérifier les permissions (si applicable)
      if (defaultTargetPlatform == TargetPlatform.android) {
        warnings.add('Vérifiez les permissions Android dans AndroidManifest.xml');
      }

      return env;
    } catch (e) {
      warnings.add('Erreur lors de la vérification de l\'environnement: ${e.toString()}');
      return env;
    }
  }

  /// Hacher le PIN (même méthode que AuthProvider)
  static String _hashPin(String pin) {
    final bytes = utf8.encode(pin);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Nettoyer les données de test
  static Future<void> cleanupTestData() async {
    try {
      final db = await DatabaseHelper.instance.database;
      await db.delete('users', where: 'full_name LIKE ?', whereArgs: ['%Test%']);
    } catch (e) {
      print('Erreur lors du nettoyage: $e');
    }
  }

  /// Afficher le diagnostic de manière lisible
  static String formatDiagnosis(Map<String, dynamic> diagnosis) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== DIAGNOSTIC DE CRÉATION DE COMPTE ===');
    buffer.writeln('Timestamp: ${diagnosis['timestamp']}');
    buffer.writeln();
    
    buffer.writeln('📋 DONNÉES D\'ENTRÉE:');
    final inputData = diagnosis['input_data'] as Map<String, dynamic>;
    inputData.forEach((key, value) {
      buffer.writeln('  $key: $value');
    });
    buffer.writeln();
    
    buffer.writeln('✅ VÉRIFICATIONS:');
    final checks = diagnosis['checks'] as Map<String, dynamic>;
    checks.forEach((key, value) {
      final status = value == true ? '✅' : (value == false ? '❌' : '⚠️');
      buffer.writeln('  $status $key: $value');
    });
    buffer.writeln();
    
    final errors = diagnosis['errors'] as List<String>;
    if (errors.isNotEmpty) {
      buffer.writeln('❌ ERREURS:');
      for (final error in errors) {
        buffer.writeln('  • $error');
      }
      buffer.writeln();
    }
    
    final warnings = diagnosis['warnings'] as List<String>;
    if (warnings.isNotEmpty) {
      buffer.writeln('⚠️ AVERTISSEMENTS:');
      for (final warning in warnings) {
        buffer.writeln('  • $warning');
      }
      buffer.writeln();
    }
    
    buffer.writeln('🎯 RÉSULTAT: ${diagnosis['success'] ? 'SUCCÈS' : 'ÉCHEC'}');
    
    return buffer.toString();
  }
}
