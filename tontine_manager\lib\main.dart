import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tontine_manager/core/database/database_helper.dart';
import 'package:tontine_manager/features/auth/providers/auth_provider.dart';
import 'package:tontine_manager/features/tontines/providers/tontine_provider.dart';
import 'package:tontine_manager/features/auth/screens/auth_screen.dart';
import 'package:tontine_manager/features/dashboard/screens/dashboard_screen.dart';
import 'package:tontine_manager/core/constants/app_colors.dart';
import 'package:google_fonts/google_fonts.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialiser la base de données
  await DatabaseHelper.instance.database;
  
  runApp(const TontineManagerApp());
}

class TontineManagerApp extends StatelessWidget {
  const TontineManagerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => TontineProvider()),
      ],
      child: MaterialApp(
        title: 'Tontine Manager',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          primaryColor: AppColors.primary,
          scaffoldBackgroundColor: AppColors.background,
          textTheme: GoogleFonts.robotoTextTheme(),
          appBarTheme: AppBarTheme(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            elevation: 0,
            titleTextStyle: GoogleFonts.roboto(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        home: Consumer<AuthProvider>(
          builder: (context, authProvider, _) {
            return authProvider.isAuthenticated 
                ? const DashboardScreen()
                : const AuthScreen();
          },
        ),
      ),
    );
  }
}
