import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  static DatabaseHelper get instance => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'tontine_manager.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // Table des utilisateurs
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        phone_number TEXT UNIQUE NOT NULL,
        full_name TEXT NOT NULL,
        profile_photo TEXT,
        pin_hash TEXT NOT NULL,
        biometric_enabled INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        last_active TEXT
      )
    ''');

    // Table des types de tontines
    await db.execute('''
      CREATE TABLE tontine_types (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        rules TEXT
      )
    ''');

    // Table des tontines
    await db.execute('''
      CREATE TABLE tontines (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        creator_id TEXT NOT NULL,
        contribution_amount REAL NOT NULL,
        frequency TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT,
        max_members INTEGER NOT NULL,
        status TEXT DEFAULT 'active',
        created_at TEXT NOT NULL,
        rules TEXT,
        FOREIGN KEY (creator_id) REFERENCES users (id)
      )
    ''');

    // Table des membres des tontines
    await db.execute('''
      CREATE TABLE tontine_members (
        id TEXT PRIMARY KEY,
        tontine_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        role TEXT NOT NULL,
        join_date TEXT NOT NULL,
        position_in_cycle INTEGER,
        is_active INTEGER DEFAULT 1,
        FOREIGN KEY (tontine_id) REFERENCES tontines (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        UNIQUE(tontine_id, user_id)
      )
    ''');

    // Table des cycles de distribution
    await db.execute('''
      CREATE TABLE distribution_cycles (
        id TEXT PRIMARY KEY,
        tontine_id TEXT NOT NULL,
        cycle_number INTEGER NOT NULL,
        beneficiary_id TEXT NOT NULL,
        scheduled_date TEXT NOT NULL,
        actual_date TEXT,
        total_amount REAL,
        status TEXT DEFAULT 'pending',
        FOREIGN KEY (tontine_id) REFERENCES tontines (id),
        FOREIGN KEY (beneficiary_id) REFERENCES users (id)
      )
    ''');

    // Table des cotisations
    await db.execute('''
      CREATE TABLE contributions (
        id TEXT PRIMARY KEY,
        tontine_id TEXT NOT NULL,
        member_id TEXT NOT NULL,
        cycle_id TEXT,
        amount REAL NOT NULL,
        payment_date TEXT,
        payment_method TEXT,
        status TEXT DEFAULT 'pending',
        late_fee REAL DEFAULT 0,
        due_date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        notes TEXT,
        FOREIGN KEY (tontine_id) REFERENCES tontines (id),
        FOREIGN KEY (member_id) REFERENCES users (id),
        FOREIGN KEY (cycle_id) REFERENCES distribution_cycles (id)
      )
    ''');

    // Insérer les types de tontines par défaut
    await _insertDefaultTontineTypes(db);
  }

  Future<void> _insertDefaultTontineTypes(Database db) async {
    final tontineTypes = [
      {
        'id': 'natt',
        'name': 'Natt',
        'description': 'Tontine traditionnelle sénégalaise avec règles strictes',
        'rules': '{"rotation_strict": true, "penalties_enabled": true}'
      },
      {
        'id': 'mbotay',
        'name': 'Mbotay',
        'description': 'Système rotatif avec épargne collective',
        'rules': '{"collective_savings": true, "flexible_withdrawal": false}'
      },
      {
        'id': 'classic',
        'name': 'Tontine Classique',
        'description': 'Tontine standard avec rotation simple',
        'rules': '{"rotation_strict": false, "penalties_enabled": false}'
      },
      {
        'id': 'family',
        'name': 'Tontine Familiale',
        'description': 'Tontine entre membres d\'une même famille',
        'rules': '{"family_only": true, "flexible_rules": true}'
      },
      {
        'id': 'commercial',
        'name': 'Tontine Commerciale',
        'description': 'Tontine adaptée aux besoins des commerçants',
        'rules': '{"business_focused": true, "higher_amounts": true}'
      },
    ];

    for (var type in tontineTypes) {
      await db.insert('tontine_types', type);
    }
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Gérer les migrations de base de données ici
    if (oldVersion < newVersion) {
      // Ajouter les migrations futures ici
    }
  }

  // Méthodes utilitaires pour fermer la base de données
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  // Méthode pour supprimer la base de données (utile pour les tests)
  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), 'tontine_manager.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
