# Script d'installation automatique de Flutter pour Windows
# Exécuter en tant qu'administrateur : Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

Write-Host "🚀 Installation de Flutter pour Tontine Manager" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Créer le dossier de développement
$devPath = "C:\dev"
if (!(Test-Path $devPath)) {
    Write-Host "📁 Création du dossier $devPath..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $devPath -Force
}

# URL de téléchargement Flutter (version stable)
$flutterUrl = "https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.16.5-stable.zip"
$flutterZip = "$devPath\flutter.zip"
$flutterPath = "$devPath\flutter"

Write-Host "⬇️  Téléchargement de Flutter..." -ForegroundColor Yellow
try {
    # Télécharger Flutter
    Invoke-WebRequest -Uri $flutterUrl -OutFile $flutterZip -UseBasicParsing
    Write-Host "✅ Téléchargement terminé" -ForegroundColor Green
    
    # Extraire l'archive
    Write-Host "📦 Extraction de Flutter..." -ForegroundColor Yellow
    Expand-Archive -Path $flutterZip -DestinationPath $devPath -Force
    
    # Supprimer le fichier zip
    Remove-Item $flutterZip -Force
    
    Write-Host "✅ Flutter extrait dans $flutterPath" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors du téléchargement: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "📝 Veuillez télécharger manuellement depuis https://flutter.dev" -ForegroundColor Yellow
    exit 1
}

# Ajouter Flutter au PATH
$flutterBin = "$flutterPath\bin"
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

if ($currentPath -notlike "*$flutterBin*") {
    Write-Host "🔧 Ajout de Flutter au PATH..." -ForegroundColor Yellow
    $newPath = "$currentPath;$flutterBin"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    Write-Host "✅ Flutter ajouté au PATH" -ForegroundColor Green
    Write-Host "⚠️  Redémarrez votre terminal pour que les changements prennent effet" -ForegroundColor Yellow
} else {
    Write-Host "✅ Flutter déjà dans le PATH" -ForegroundColor Green
}

# Vérifier l'installation
Write-Host "🔍 Vérification de l'installation..." -ForegroundColor Yellow
$env:PATH = "$env:PATH;$flutterBin"

try {
    & "$flutterBin\flutter.bat" --version
    Write-Host "✅ Flutter installé avec succès !" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Flutter installé mais nécessite un redémarrage du terminal" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Installation terminée !" -ForegroundColor Green
Write-Host "📋 Prochaines étapes :" -ForegroundColor Cyan
Write-Host "   1. Redémarrez votre terminal PowerShell" -ForegroundColor White
Write-Host "   2. Exécutez: flutter doctor" -ForegroundColor White
Write-Host "   3. Naviguez vers le projet: cd tontine_manager" -ForegroundColor White
Write-Host "   4. Installez les dépendances: flutter pub get" -ForegroundColor White
Write-Host "   5. Lancez l'app: flutter run" -ForegroundColor White
Write-Host ""
Write-Host "💡 Si vous rencontrez des problèmes, consultez: https://docs.flutter.dev/get-started/install/windows" -ForegroundColor Cyan
