enum ContributionStatus {
  pending,    // En attente
  paid,       // Payé
  late,       // En retard
  cancelled,  // Annulé
}

enum PaymentMethod {
  cash,         // Espèces
  mobileWallet, // Portefeuille mobile (Orange Money, Wave, etc.)
  bankTransfer, // Virement bancaire
}

class Contribution {
  final String id;
  final String tontineId;
  final String memberId;
  final String? cycleId;
  final double amount;
  final DateTime? paymentDate;
  final PaymentMethod? paymentMethod;
  final ContributionStatus status;
  final double lateFee;
  final DateTime dueDate;
  final DateTime createdAt;
  final String? notes;

  Contribution({
    required this.id,
    required this.tontineId,
    required this.memberId,
    this.cycleId,
    required this.amount,
    this.paymentDate,
    this.paymentMethod,
    this.status = ContributionStatus.pending,
    this.lateFee = 0.0,
    required this.dueDate,
    required this.createdAt,
    this.notes,
  });

  // Conversion vers Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tontine_id': tontineId,
      'member_id': memberId,
      'cycle_id': cycleId,
      'amount': amount,
      'payment_date': paymentDate?.toIso8601String(),
      'payment_method': paymentMethod?.name,
      'status': status.name,
      'late_fee': lateFee,
      'due_date': dueDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'notes': notes,
    };
  }

  // Création depuis Map (base de données)
  factory Contribution.fromMap(Map<String, dynamic> map) {
    return Contribution(
      id: map['id'] as String,
      tontineId: map['tontine_id'] as String,
      memberId: map['member_id'] as String,
      cycleId: map['cycle_id'] as String?,
      amount: (map['amount'] as num).toDouble(),
      paymentDate: map['payment_date'] != null 
          ? DateTime.parse(map['payment_date'] as String)
          : null,
      paymentMethod: map['payment_method'] != null
          ? PaymentMethod.values.firstWhere(
              (e) => e.name == map['payment_method'],
              orElse: () => PaymentMethod.cash,
            )
          : null,
      status: ContributionStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ContributionStatus.pending,
      ),
      lateFee: (map['late_fee'] as num?)?.toDouble() ?? 0.0,
      dueDate: DateTime.parse(map['due_date'] as String),
      createdAt: DateTime.parse(map['created_at'] as String),
      notes: map['notes'] as String?,
    );
  }

  // Nom d'affichage du statut
  String get statusDisplayName {
    switch (status) {
      case ContributionStatus.pending:
        return 'En attente';
      case ContributionStatus.paid:
        return 'Payé';
      case ContributionStatus.late:
        return 'En retard';
      case ContributionStatus.cancelled:
        return 'Annulé';
    }
  }

  // Nom d'affichage de la méthode de paiement
  String get paymentMethodDisplayName {
    if (paymentMethod == null) return 'Non spécifié';
    
    switch (paymentMethod!) {
      case PaymentMethod.cash:
        return 'Espèces';
      case PaymentMethod.mobileWallet:
        return 'Portefeuille mobile';
      case PaymentMethod.bankTransfer:
        return 'Virement bancaire';
    }
  }

  // Vérifier si la contribution est en retard
  bool get isLate {
    if (status == ContributionStatus.paid) return false;
    return DateTime.now().isAfter(dueDate);
  }

  // Calculer le nombre de jours de retard
  int get daysLate {
    if (!isLate) return 0;
    return DateTime.now().difference(dueDate).inDays;
  }

  // Montant total (contribution + pénalité)
  double get totalAmount => amount + lateFee;

  // Copie avec modifications
  Contribution copyWith({
    String? id,
    String? tontineId,
    String? memberId,
    String? cycleId,
    double? amount,
    DateTime? paymentDate,
    PaymentMethod? paymentMethod,
    ContributionStatus? status,
    double? lateFee,
    DateTime? dueDate,
    DateTime? createdAt,
    String? notes,
  }) {
    return Contribution(
      id: id ?? this.id,
      tontineId: tontineId ?? this.tontineId,
      memberId: memberId ?? this.memberId,
      cycleId: cycleId ?? this.cycleId,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      lateFee: lateFee ?? this.lateFee,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'Contribution(id: $id, amount: $amount, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Contribution && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
