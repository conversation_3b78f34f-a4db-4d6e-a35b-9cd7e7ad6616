import 'package:sqflite/sqflite.dart';
import 'package:tontine_manager/core/database/database_helper.dart';
import 'package:tontine_manager/shared/models/user.dart';

class AuthService {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;

  // Créer un nouvel utilisateur
  Future<bool> createUser(User user) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.insert(
        'users',
        user.toMap(),
        conflictAlgorithm: ConflictAlgorithm.abort,
      );
      return result > 0;
    } catch (e) {
      print('Erreur lors de la création de l\'utilisateur: $e');
      return false;
    }
  }

  // Récupérer un utilisateur par son ID
  Future<User?> getUserById(String id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'users',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return User.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      print('Erreur lors de la récupération de l\'utilisateur: $e');
      return null;
    }
  }

  // Récupérer un utilisateur par son numéro de téléphone
  Future<User?> getUserByPhone(String phoneNumber) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'users',
        where: 'phone_number = ?',
        whereArgs: [phoneNumber],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return User.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      print('Erreur lors de la récupération de l\'utilisateur par téléphone: $e');
      return null;
    }
  }

  // Mettre à jour un utilisateur
  Future<bool> updateUser(User user) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.update(
        'users',
        user.toMap(),
        where: 'id = ?',
        whereArgs: [user.id],
      );
      return result > 0;
    } catch (e) {
      print('Erreur lors de la mise à jour de l\'utilisateur: $e');
      return false;
    }
  }

  // Supprimer un utilisateur
  Future<bool> deleteUser(String id) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.delete(
        'users',
        where: 'id = ?',
        whereArgs: [id],
      );
      return result > 0;
    } catch (e) {
      print('Erreur lors de la suppression de l\'utilisateur: $e');
      return false;
    }
  }

  // Vérifier si un numéro de téléphone existe déjà
  Future<bool> phoneNumberExists(String phoneNumber) async {
    try {
      final user = await getUserByPhone(phoneNumber);
      return user != null;
    } catch (e) {
      print('Erreur lors de la vérification du numéro de téléphone: $e');
      return false;
    }
  }

  // Récupérer tous les utilisateurs (pour les tests ou l'administration)
  Future<List<User>> getAllUsers() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('users');
      
      return List.generate(maps.length, (i) {
        return User.fromMap(maps[i]);
      });
    } catch (e) {
      print('Erreur lors de la récupération de tous les utilisateurs: $e');
      return [];
    }
  }

  // Rechercher des utilisateurs par nom
  Future<List<User>> searchUsersByName(String name) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'users',
        where: 'full_name LIKE ?',
        whereArgs: ['%$name%'],
      );
      
      return List.generate(maps.length, (i) {
        return User.fromMap(maps[i]);
      });
    } catch (e) {
      print('Erreur lors de la recherche d\'utilisateurs: $e');
      return [];
    }
  }
}
