import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'package:tontine_manager/shared/models/user.dart';
import 'package:tontine_manager/features/auth/services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  User? _currentUser;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  User? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  AuthProvider() {
    _checkAuthStatus();
  }

  // Vérifier le statut d'authentification au démarrage
  Future<void> _checkAuthStatus() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('current_user_id');
      
      if (userId != null) {
        final user = await _authService.getUserById(userId);
        if (user != null) {
          _currentUser = user;
          _isAuthenticated = true;
          await _updateLastActive();
        }
      }
    } catch (e) {
      _setError('Erreur lors de la vérification de l\'authentification');
    } finally {
      _setLoading(false);
    }
  }

  // Inscription d'un nouvel utilisateur
  Future<bool> register({
    required String phoneNumber,
    required String fullName,
    required String pin,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Vérifier si l'utilisateur existe déjà
      final existingUser = await _authService.getUserByPhone(phoneNumber);
      if (existingUser != null) {
        _setError('Un compte existe déjà avec ce numéro de téléphone');
        return false;
      }

      // Créer le nouvel utilisateur
      final user = User(
        id: const Uuid().v4(),
        phoneNumber: phoneNumber,
        fullName: fullName,
        pinHash: _hashPin(pin),
        createdAt: DateTime.now(),
      );

      final success = await _authService.createUser(user);
      if (success) {
        _currentUser = user;
        _isAuthenticated = true;
        await _saveAuthStatus(user.id);
        return true;
      } else {
        _setError('Erreur lors de la création du compte');
        return false;
      }
    } catch (e) {
      _setError('Erreur lors de l\'inscription: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Connexion d'un utilisateur existant
  Future<bool> login({
    required String phoneNumber,
    required String pin,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final user = await _authService.getUserByPhone(phoneNumber);
      if (user == null) {
        _setError('Aucun compte trouvé avec ce numéro de téléphone');
        return false;
      }

      if (!_verifyPin(pin, user.pinHash)) {
        _setError('Code PIN incorrect');
        return false;
      }

      _currentUser = user;
      _isAuthenticated = true;
      await _saveAuthStatus(user.id);
      await _updateLastActive();
      return true;
    } catch (e) {
      _setError('Erreur lors de la connexion: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Déconnexion
  Future<void> logout() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user_id');
      
      _currentUser = null;
      _isAuthenticated = false;
      _clearError();
    } catch (e) {
      _setError('Erreur lors de la déconnexion');
    } finally {
      _setLoading(false);
    }
  }

  // Changer le PIN
  Future<bool> changePin({
    required String currentPin,
    required String newPin,
  }) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      if (!_verifyPin(currentPin, _currentUser!.pinHash)) {
        _setError('Code PIN actuel incorrect');
        return false;
      }

      final updatedUser = _currentUser!.copyWith(
        pinHash: _hashPin(newPin),
      );

      final success = await _authService.updateUser(updatedUser);
      if (success) {
        _currentUser = updatedUser;
        return true;
      } else {
        _setError('Erreur lors de la modification du PIN');
        return false;
      }
    } catch (e) {
      _setError('Erreur lors de la modification du PIN: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Activer/désactiver la biométrie
  Future<bool> toggleBiometric(bool enabled) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    try {
      final updatedUser = _currentUser!.copyWith(
        biometricEnabled: enabled,
      );

      final success = await _authService.updateUser(updatedUser);
      if (success) {
        _currentUser = updatedUser;
        return true;
      }
      return false;
    } catch (e) {
      _setError('Erreur lors de la configuration biométrique');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Méthodes utilitaires privées
  String _hashPin(String pin) {
    final bytes = utf8.encode(pin);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  bool _verifyPin(String pin, String hashedPin) {
    return _hashPin(pin) == hashedPin;
  }

  Future<void> _saveAuthStatus(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('current_user_id', userId);
  }

  Future<void> _updateLastActive() async {
    if (_currentUser == null) return;
    
    final updatedUser = _currentUser!.copyWith(
      lastActive: DateTime.now(),
    );
    
    await _authService.updateUser(updatedUser);
    _currentUser = updatedUser;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
