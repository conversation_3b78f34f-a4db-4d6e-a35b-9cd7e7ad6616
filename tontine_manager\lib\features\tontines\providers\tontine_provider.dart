import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import 'package:tontine_manager/shared/models/tontine.dart';
import 'package:tontine_manager/shared/models/member.dart';
import 'package:tontine_manager/features/tontines/services/tontine_service.dart';

class TontineProvider extends ChangeNotifier {
  final TontineService _tontineService = TontineService();
  
  List<Tontine> _tontines = [];
  List<Member> _currentTontineMembers = [];
  Tontine? _selectedTontine;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Tontine> get tontines => _tontines;
  List<Member> get currentTontineMembers => _currentTontineMembers;
  Tontine? get selectedTontine => _selectedTontine;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Récupérer toutes les tontines d'un utilisateur
  Future<void> loadUserTontines(String userId) async {
    _setLoading(true);
    _clearError();

    try {
      _tontines = await _tontineService.getUserTontines(userId);
    } catch (e) {
      _setError('Erreur lors du chargement des tontines: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Créer une nouvelle tontine
  Future<bool> createTontine({
    required String name,
    required TontineType type,
    required String creatorId,
    required double contributionAmount,
    required TontineFrequency frequency,
    required DateTime startDate,
    required int maxMembers,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final tontine = Tontine(
        id: const Uuid().v4(),
        name: name,
        type: type,
        creatorId: creatorId,
        contributionAmount: contributionAmount,
        frequency: frequency,
        startDate: startDate,
        maxMembers: maxMembers,
        createdAt: DateTime.now(),
      );

      final success = await _tontineService.createTontine(tontine);
      if (success) {
        // Ajouter le créateur comme gestionnaire
        final creatorMember = Member(
          id: const Uuid().v4(),
          tontineId: tontine.id,
          userId: creatorId,
          role: MemberRole.manager,
          joinDate: DateTime.now(),
          positionInCycle: 1,
        );

        await _tontineService.addMember(creatorMember);
        
        // Recharger les tontines
        await loadUserTontines(creatorId);
        return true;
      }
      return false;
    } catch (e) {
      _setError('Erreur lors de la création de la tontine: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Sélectionner une tontine et charger ses membres
  Future<void> selectTontine(String tontineId) async {
    _setLoading(true);
    _clearError();

    try {
      _selectedTontine = await _tontineService.getTontineById(tontineId);
      if (_selectedTontine != null) {
        _currentTontineMembers = await _tontineService.getTontineMembers(tontineId);
      }
    } catch (e) {
      _setError('Erreur lors de la sélection de la tontine: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Ajouter un membre à une tontine
  Future<bool> addMemberToTontine({
    required String tontineId,
    required String userId,
    required MemberRole role,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Vérifier si l'utilisateur est déjà membre
      final existingMember = await _tontineService.getMemberByUserAndTontine(userId, tontineId);
      if (existingMember != null) {
        _setError('Cet utilisateur est déjà membre de cette tontine');
        return false;
      }

      // Vérifier si la tontine n'est pas pleine
      final members = await _tontineService.getTontineMembers(tontineId);
      final tontine = await _tontineService.getTontineById(tontineId);
      
      if (tontine != null && members.length >= tontine.maxMembers) {
        _setError('Cette tontine a atteint son nombre maximum de membres');
        return false;
      }

      final member = Member(
        id: const Uuid().v4(),
        tontineId: tontineId,
        userId: userId,
        role: role,
        joinDate: DateTime.now(),
        positionInCycle: members.length + 1,
      );

      final success = await _tontineService.addMember(member);
      if (success) {
        // Recharger les membres si c'est la tontine sélectionnée
        if (_selectedTontine?.id == tontineId) {
          _currentTontineMembers = await _tontineService.getTontineMembers(tontineId);
        }
        return true;
      }
      return false;
    } catch (e) {
      _setError('Erreur lors de l\'ajout du membre: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Mettre à jour une tontine
  Future<bool> updateTontine(Tontine tontine) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _tontineService.updateTontine(tontine);
      if (success) {
        // Mettre à jour la liste locale
        final index = _tontines.indexWhere((t) => t.id == tontine.id);
        if (index != -1) {
          _tontines[index] = tontine;
        }
        
        // Mettre à jour la tontine sélectionnée si nécessaire
        if (_selectedTontine?.id == tontine.id) {
          _selectedTontine = tontine;
        }
        
        return true;
      }
      return false;
    } catch (e) {
      _setError('Erreur lors de la mise à jour de la tontine: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Supprimer une tontine
  Future<bool> deleteTontine(String tontineId) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _tontineService.deleteTontine(tontineId);
      if (success) {
        _tontines.removeWhere((t) => t.id == tontineId);
        if (_selectedTontine?.id == tontineId) {
          _selectedTontine = null;
          _currentTontineMembers.clear();
        }
        return true;
      }
      return false;
    } catch (e) {
      _setError('Erreur lors de la suppression de la tontine: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Récupérer les tontines actives d'un utilisateur
  List<Tontine> getActiveTontines() {
    return _tontines.where((t) => t.status == TontineStatus.active).toList();
  }

  // Récupérer les statistiques d'un utilisateur
  Map<String, dynamic> getUserStats(String userId) {
    final activeTontines = getActiveTontines();
    double totalContributed = 0;
    double totalReceived = 0;
    
    // Ces calculs seront plus précis avec les données de cotisations
    for (var tontine in activeTontines) {
      totalContributed += tontine.contributionAmount;
    }

    return {
      'activeTontines': activeTontines.length,
      'totalContributed': totalContributed,
      'totalReceived': totalReceived,
    };
  }

  // Méthodes utilitaires privées
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Nettoyer les données
  void clear() {
    _tontines.clear();
    _currentTontineMembers.clear();
    _selectedTontine = null;
    _clearError();
    notifyListeners();
  }
}
