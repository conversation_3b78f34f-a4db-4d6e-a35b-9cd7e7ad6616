class AppStrings {
  // Général
  static const String appName = 'Tontine Manager';
  static const String welcome = 'Bienvenue';
  static const String loading = 'Chargement...';
  static const String error = 'Erreur';
  static const String success = 'Succès';
  static const String cancel = 'Annuler';
  static const String confirm = 'Confirmer';
  static const String save = 'Enregistrer';
  static const String delete = 'Supprimer';
  static const String edit = 'Modifier';
  static const String add = 'Ajouter';
  
  // Authentification
  static const String login = 'Se connecter';
  static const String register = 'S\'inscrire';
  static const String phoneNumber = 'Numéro de téléphone';
  static const String pin = 'Code PIN';
  static const String confirmPin = 'Confirmer le PIN';
  static const String fullName = 'Nom complet';
  static const String biometricAuth = 'Authentification biométrique';
  static const String enableBiometric = 'Activer la biométrie';
  static const String loginWithBiometric = 'Se connecter avec l\'empreinte';
  static const String noAccount = 'Pas de compte ?';
  static const String hasAccount = 'Déjà un compte ?';
  
  // Tableau de bord
  static const String dashboard = 'Tableau de bord';
  static const String myTontines = 'Mes Tontines';
  static const String createTontine = 'Créer une Tontine';
  static const String nextTurn = 'Prochain tour';
  static const String monthlyStats = 'Statistiques du mois';
  static const String totalContributed = 'Total cotisé';
  static const String totalReceived = 'Total reçu';
  
  // Tontines
  static const String tontineName = 'Nom de la tontine';
  static const String tontineType = 'Type de tontine';
  static const String contributionAmount = 'Montant de cotisation';
  static const String frequency = 'Fréquence';
  static const String maxMembers = 'Nombre maximum de membres';
  static const String startDate = 'Date de début';
  static const String members = 'Membres';
  static const String addMember = 'Ajouter un membre';
  
  // Types de tontines
  static const String natt = 'Natt';
  static const String mbotay = 'Mbotay';
  static const String classicTontine = 'Tontine Classique';
  static const String familyTontine = 'Tontine Familiale';
  static const String commercialTontine = 'Tontine Commerciale';
  
  // Rôles
  static const String manager = 'Gestionnaire';
  static const String collector = 'Collecteur';
  static const String secretary = 'Secrétaire';
  static const String member = 'Membre';
  
  // Fréquences
  static const String weekly = 'Hebdomadaire';
  static const String monthly = 'Mensuel';
  static const String biweekly = 'Bi-hebdomadaire';
  
  // Cotisations
  static const String contributions = 'Cotisations';
  static const String recordPayment = 'Enregistrer paiement';
  static const String sendReminder = 'Envoyer rappel';
  static const String paid = 'Payé';
  static const String pending = 'En attente';
  static const String late = 'En retard';
  static const String totalCollected = 'Total collecté';
  static const String objective = 'Objectif';
  static const String nextMeeting = 'Prochaine réunion';
  
  // Profil
  static const String profile = 'Profil';
  static const String settings = 'Paramètres';
  static const String security = 'Sécurité';
  static const String notifications = 'Notifications';
  static const String support = 'Support';
  static const String language = 'Langue';
  static const String logout = 'Déconnexion';
  static const String punctualityRate = 'Taux de ponctualité';
  
  // Historique
  static const String history = 'Historique';
  static const String transactions = 'Transactions';
  static const String exportPdf = 'Exporter PDF';
  static const String filter = 'Filtrer';
  static const String contribution = 'Cotisation';
  static const String distribution = 'Distribution';
  
  // Messages
  static const String loginSuccess = 'Connexion réussie';
  static const String loginError = 'Erreur de connexion';
  static const String registrationSuccess = 'Inscription réussie';
  static const String invalidPin = 'Code PIN invalide';
  static const String pinMismatch = 'Les codes PIN ne correspondent pas';
  static const String phoneRequired = 'Numéro de téléphone requis';
  static const String nameRequired = 'Nom complet requis';
  
  // Devises
  static const String fcfa = 'FCFA';
  static const String currency = 'F CFA';
  
  // Salutations culturelles
  static const String greetingMorning = 'Bonjour';
  static const String greetingAfternoon = 'Bonsoir';
  static const String greetingEvening = 'Bonne nuit';
  
  // Expressions sénégalaises
  static const String inshallah = 'Inch\'Allah';
  static const String alhamdulillah = 'Al-Hamdou Lillah';
}
