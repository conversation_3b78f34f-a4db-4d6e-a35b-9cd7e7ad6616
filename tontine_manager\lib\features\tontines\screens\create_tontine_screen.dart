import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:tontine_manager/features/auth/providers/auth_provider.dart';
import 'package:tontine_manager/features/tontines/providers/tontine_provider.dart';
import 'package:tontine_manager/shared/models/tontine.dart';
import 'package:tontine_manager/core/constants/app_colors.dart';
import 'package:tontine_manager/core/constants/app_strings.dart';
import 'package:tontine_manager/shared/widgets/custom_button.dart';
import 'package:tontine_manager/shared/widgets/custom_text_field.dart';
import 'package:tontine_manager/shared/widgets/loading_overlay.dart';

class CreateTontineScreen extends StatefulWidget {
  const CreateTontineScreen({super.key});

  @override
  State<CreateTontineScreen> createState() => _CreateTontineScreenState();
}

class _CreateTontineScreenState extends State<CreateTontineScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _contributionController = TextEditingController();
  final _maxMembersController = TextEditingController();
  
  TontineType _selectedType = TontineType.classic;
  TontineFrequency _selectedFrequency = TontineFrequency.monthly;
  DateTime _selectedDate = DateTime.now();

  @override
  void dispose() {
    _nameController.dispose();
    _contributionController.dispose();
    _maxMembersController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.createTontine),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Consumer<TontineProvider>(
        builder: (context, tontineProvider, child) {
          return LoadingOverlay(
            isLoading: tontineProvider.isLoading,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionTitle('Informations générales'),
                    const SizedBox(height: 16),
                    _buildNameField(),
                    const SizedBox(height: 16),
                    _buildTypeSelector(),
                    const SizedBox(height: 24),
                    _buildSectionTitle('Configuration financière'),
                    const SizedBox(height: 16),
                    _buildContributionField(),
                    const SizedBox(height: 16),
                    _buildFrequencySelector(),
                    const SizedBox(height: 24),
                    _buildSectionTitle('Paramètres du groupe'),
                    const SizedBox(height: 16),
                    _buildMaxMembersField(),
                    const SizedBox(height: 16),
                    _buildDateSelector(),
                    const SizedBox(height: 32),
                    if (tontineProvider.errorMessage != null)
                      _buildErrorMessage(tontineProvider.errorMessage!),
                    const SizedBox(height: 16),
                    _buildCreateButton(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildNameField() {
    return CustomTextField(
      controller: _nameController,
      label: AppStrings.tontineName,
      hintText: 'Ex: Tontine Famille Diop',
      prefixIcon: Icons.group,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Le nom de la tontine est requis';
        }
        if (value.length < 3) {
          return 'Le nom doit contenir au moins 3 caractères';
        }
        return null;
      },
    );
  }

  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.tontineType,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.textLight.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: TontineType.values.map((type) {
              return RadioListTile<TontineType>(
                title: Text(_getTontineTypeDisplayName(type)),
                subtitle: Text(_getTontineTypeDescription(type)),
                value: type,
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
                activeColor: AppColors.primary,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildContributionField() {
    return CustomTextField(
      controller: _contributionController,
      label: AppStrings.contributionAmount,
      hintText: 'Ex: 25000',
      keyboardType: TextInputType.number,
      prefixIcon: Icons.monetization_on,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Le montant de cotisation est requis';
        }
        final amount = double.tryParse(value);
        if (amount == null || amount <= 0) {
          return 'Veuillez entrer un montant valide';
        }
        if (amount < 1000) {
          return 'Le montant minimum est de 1,000 FCFA';
        }
        return null;
      },
    );
  }

  Widget _buildFrequencySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.frequency,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.textLight.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: TontineFrequency.values.map((frequency) {
              return RadioListTile<TontineFrequency>(
                title: Text(_getFrequencyDisplayName(frequency)),
                value: frequency,
                groupValue: _selectedFrequency,
                onChanged: (value) {
                  setState(() {
                    _selectedFrequency = value!;
                  });
                },
                activeColor: AppColors.primary,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildMaxMembersField() {
    return CustomTextField(
      controller: _maxMembersController,
      label: AppStrings.maxMembers,
      hintText: 'Ex: 10',
      keyboardType: TextInputType.number,
      prefixIcon: Icons.people,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Le nombre maximum de membres est requis';
        }
        final members = int.tryParse(value);
        if (members == null || members <= 0) {
          return 'Veuillez entrer un nombre valide';
        }
        if (members < 2) {
          return 'Une tontine doit avoir au moins 2 membres';
        }
        if (members > 50) {
          return 'Le nombre maximum de membres est de 50';
        }
        return null;
      },
    );
  }

  Widget _buildDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.startDate,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.textLight.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, color: AppColors.primary),
                const SizedBox(width: 12),
                Text(
                  DateFormat('EEEE d MMMM yyyy', 'fr_FR').format(_selectedDate),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.error.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: AppColors.error, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(color: AppColors.error, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreateButton() {
    return CustomButton(
      text: 'Créer la tontine',
      icon: Icons.add,
      onPressed: _handleCreateTontine,
      isFullWidth: true,
    );
  }

  String _getTontineTypeDisplayName(TontineType type) {
    switch (type) {
      case TontineType.natt:
        return AppStrings.natt;
      case TontineType.mbotay:
        return AppStrings.mbotay;
      case TontineType.classic:
        return AppStrings.classicTontine;
      case TontineType.family:
        return AppStrings.familyTontine;
      case TontineType.commercial:
        return AppStrings.commercialTontine;
    }
  }

  String _getTontineTypeDescription(TontineType type) {
    switch (type) {
      case TontineType.natt:
        return 'Tontine traditionnelle avec règles strictes';
      case TontineType.mbotay:
        return 'Système rotatif avec épargne collective';
      case TontineType.classic:
        return 'Tontine standard avec rotation simple';
      case TontineType.family:
        return 'Entre membres d\'une même famille';
      case TontineType.commercial:
        return 'Adaptée aux besoins des commerçants';
    }
  }

  String _getFrequencyDisplayName(TontineFrequency frequency) {
    switch (frequency) {
      case TontineFrequency.weekly:
        return AppStrings.weekly;
      case TontineFrequency.biweekly:
        return AppStrings.biweekly;
      case TontineFrequency.monthly:
        return AppStrings.monthly;
    }
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('fr', 'FR'),
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _handleCreateTontine() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final tontineProvider = Provider.of<TontineProvider>(context, listen: false);

    if (authProvider.currentUser == null) return;

    final success = await tontineProvider.createTontine(
      name: _nameController.text.trim(),
      type: _selectedType,
      creatorId: authProvider.currentUser!.id,
      contributionAmount: double.parse(_contributionController.text),
      frequency: _selectedFrequency,
      startDate: _selectedDate,
      maxMembers: int.parse(_maxMembersController.text),
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tontine créée avec succès !'),
          backgroundColor: AppColors.success,
        ),
      );
      Navigator.of(context).pop();
    }
  }
}
