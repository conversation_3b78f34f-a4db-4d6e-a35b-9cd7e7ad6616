// Test automatisé pour valider les corrections de création de compte
// Exécuter avec : dart test_registration_fix.dart

import 'dart:convert';
import 'package:crypto/crypto.dart';

/// Simulation des corrections apportées à AuthProvider
class AuthProviderTest {
  
  /// Valider les données d'inscription (méthode corrigée)
  static String? validateRegistrationData(String phoneNumber, String fullName, String pin) {
    // Validation du nom complet
    if (fullName.trim().isEmpty) {
      return 'Le nom complet est requis';
    }
    if (fullName.trim().length < 2) {
      return 'Le nom doit contenir au moins 2 caractères';
    }

    // Validation du numéro de téléphone
    if (phoneNumber.trim().isEmpty) {
      return 'Le numéro de téléphone est requis';
    }
    
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanNumber.length < 9) {
      return 'Numéro de téléphone invalide (minimum 9 chiffres)';
    }
    if (cleanNumber.length > 15) {
      return 'Numéro de téléphone trop long (maximum 15 chiffres)';
    }

    // Validation du PIN
    if (pin.trim().isEmpty) {
      return 'Le code PIN est requis';
    }
    if (pin.length != 4) {
      return 'Le code PIN doit contenir exactement 4 chiffres';
    }
    if (!RegExp(r'^\d{4}$').hasMatch(pin)) {
      return 'Le code PIN doit contenir uniquement des chiffres';
    }

    return null; // Aucune erreur
  }

  /// Nettoyer le numéro de téléphone (méthode corrigée)
  static String cleanPhoneNumber(String phoneNumber) {
    // Supprimer tous les caractères non numériques
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Si le numéro commence par 221 (indicatif Sénégal), le garder
    // Sinon, ajouter 221 si c'est un numéro local sénégalais
    if (cleaned.startsWith('221')) {
      return cleaned;
    } else if (cleaned.length == 9 && cleaned.startsWith('7')) {
      // Numéro local sénégalais (commence par 7)
      return '221$cleaned';
    }
    
    return cleaned;
  }

  /// Obtenir un message d'erreur lisible (méthode corrigée)
  static String getReadableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('unique constraint')) {
      return 'Ce numéro de téléphone est déjà utilisé';
    } else if (errorString.contains('database')) {
      return 'Erreur de base de données. Veuillez réessayer';
    } else if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Problème de connexion. Vérifiez votre réseau';
    } else if (errorString.contains('permission')) {
      return 'Permissions insuffisantes. Vérifiez les paramètres de l\'app';
    } else if (errorString.contains('timeout')) {
      return 'Délai d\'attente dépassé. Veuillez réessayer';
    } else {
      return 'Une erreur inattendue s\'est produite';
    }
  }

  /// Hacher le PIN
  static String hashPin(String pin) {
    final bytes = utf8.encode(pin);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}

/// Tests de validation
void main() {
  print('🧪 Test des Corrections de Création de Compte');
  print('==============================================');

  // Test 1: Validation des données d'inscription
  print('\n📋 Test 1: Validation des données d\'inscription');
  final validationTests = [
    // Cas valides
    {'phone': '221771234567', 'name': 'Fatou Diop Sarr', 'pin': '1234', 'expected': null},
    {'phone': '+221 77 123 45 67', 'name': 'Aminata Fall', 'pin': '5678', 'expected': null},
    {'phone': '77 123 45 67', 'name': 'Moussa Sow', 'pin': '9999', 'expected': null},
    
    // Cas invalides - Nom
    {'phone': '221771234567', 'name': '', 'pin': '1234', 'expected': 'Le nom complet est requis'},
    {'phone': '221771234567', 'name': 'A', 'pin': '1234', 'expected': 'Le nom doit contenir au moins 2 caractères'},
    
    // Cas invalides - Téléphone
    {'phone': '', 'name': 'Fatou Diop', 'pin': '1234', 'expected': 'Le numéro de téléphone est requis'},
    {'phone': '123', 'name': 'Fatou Diop', 'pin': '1234', 'expected': 'Numéro de téléphone invalide (minimum 9 chiffres)'},
    {'phone': '12345678901234567890', 'name': 'Fatou Diop', 'pin': '1234', 'expected': 'Numéro de téléphone trop long (maximum 15 chiffres)'},
    
    // Cas invalides - PIN
    {'phone': '221771234567', 'name': 'Fatou Diop', 'pin': '', 'expected': 'Le code PIN est requis'},
    {'phone': '221771234567', 'name': 'Fatou Diop', 'pin': '12', 'expected': 'Le code PIN doit contenir exactement 4 chiffres'},
    {'phone': '221771234567', 'name': 'Fatou Diop', 'pin': '12345', 'expected': 'Le code PIN doit contenir exactement 4 chiffres'},
    {'phone': '221771234567', 'name': 'Fatou Diop', 'pin': 'abcd', 'expected': 'Le code PIN doit contenir uniquement des chiffres'},
  ];

  int validationPassed = 0;
  for (int i = 0; i < validationTests.length; i++) {
    final test = validationTests[i];
    final result = AuthProviderTest.validateRegistrationData(
      test['phone'] as String,
      test['name'] as String,
      test['pin'] as String,
    );
    
    final expected = test['expected'] as String?;
    final passed = result == expected;
    final status = passed ? '✅' : '❌';
    
    print('$status Test ${i + 1}: ${test['name']}, ${test['phone']}, ${test['pin']} -> ${result ?? 'VALIDE'}');
    
    if (passed) validationPassed++;
  }

  print('\nRésultat validation: $validationPassed/${validationTests.length} tests réussis');

  // Test 2: Nettoyage des numéros de téléphone
  print('\n📱 Test 2: Nettoyage des numéros de téléphone');
  final phoneTests = [
    {'input': '221771234567', 'expected': '221771234567'},
    {'input': '+221 77 123 45 67', 'expected': '221771234567'},
    {'input': '77 123 45 67', 'expected': '221771234567'},
    {'input': '77-123-45-67', 'expected': '221771234567'},
    {'input': '(77) 123 45 67', 'expected': '221771234567'},
    {'input': '221781234567', 'expected': '221781234567'},
    {'input': '123456789', 'expected': '123456789'}, // Numéro non sénégalais
  ];

  int phonePassed = 0;
  for (int i = 0; i < phoneTests.length; i++) {
    final test = phoneTests[i];
    final result = AuthProviderTest.cleanPhoneNumber(test['input'] as String);
    final expected = test['expected'] as String;
    final passed = result == expected;
    final status = passed ? '✅' : '❌';
    
    print('$status Test ${i + 1}: "${test['input']}" -> "$result" (attendu: "$expected")');
    
    if (passed) phonePassed++;
  }

  print('\nRésultat nettoyage: $phonePassed/${phoneTests.length} tests réussis');

  // Test 3: Messages d'erreur lisibles
  print('\n💬 Test 3: Messages d\'erreur lisibles');
  final errorTests = [
    {'error': 'UNIQUE constraint failed: users.phone_number', 'expected': 'Ce numéro de téléphone est déjà utilisé'},
    {'error': 'database is locked', 'expected': 'Erreur de base de données. Veuillez réessayer'},
    {'error': 'network error occurred', 'expected': 'Problème de connexion. Vérifiez votre réseau'},
    {'error': 'permission denied', 'expected': 'Permissions insuffisantes. Vérifiez les paramètres de l\'app'},
    {'error': 'timeout exceeded', 'expected': 'Délai d\'attente dépassé. Veuillez réessayer'},
    {'error': 'unknown error xyz', 'expected': 'Une erreur inattendue s\'est produite'},
  ];

  int errorPassed = 0;
  for (int i = 0; i < errorTests.length; i++) {
    final test = errorTests[i];
    final result = AuthProviderTest.getReadableError(test['error'] as String);
    final expected = test['expected'] as String;
    final passed = result == expected;
    final status = passed ? '✅' : '❌';
    
    print('$status Test ${i + 1}: "${test['error']}" -> "$result"');
    
    if (passed) errorPassed++;
  }

  print('\nRésultat messages: $errorPassed/${errorTests.length} tests réussis');

  // Test 4: Hachage PIN
  print('\n🔐 Test 4: Hachage des codes PIN');
  final pinTests = ['1234', '5678', '0000', '9999'];
  
  for (final pin in pinTests) {
    final hashed = AuthProviderTest.hashPin(pin);
    final isValid = hashed.isNotEmpty && hashed.length == 64; // SHA-256 = 64 caractères hex
    final status = isValid ? '✅' : '❌';
    print('$status PIN "$pin" -> ${hashed.substring(0, 16)}... (${hashed.length} caractères)');
  }

  // Résumé final
  print('\n🎉 RÉSUMÉ DES TESTS');
  print('==================');
  final totalTests = validationTests.length + phoneTests.length + errorTests.length + pinTests.length;
  final totalPassed = validationPassed + phonePassed + errorPassed + pinTests.length;
  
  print('✅ Validation des données: $validationPassed/${validationTests.length}');
  print('✅ Nettoyage téléphone: $phonePassed/${phoneTests.length}');
  print('✅ Messages d\'erreur: $errorPassed/${errorTests.length}');
  print('✅ Hachage PIN: ${pinTests.length}/${pinTests.length}');
  print('');
  print('🎯 TOTAL: $totalPassed/$totalTests tests réussis');
  
  if (totalPassed == totalTests) {
    print('🎉 TOUS LES TESTS SONT RÉUSSIS !');
    print('✅ Les corrections sont validées et prêtes pour l\'application.');
  } else {
    print('⚠️  Certains tests ont échoué. Vérifiez les corrections.');
  }

  // Scénario de test complet
  print('\n🧪 SCÉNARIO DE TEST COMPLET');
  print('===========================');
  print('Données de test recommandées pour l\'application :');
  print('- Nom complet: "Fatou Diop Sarr"');
  print('- Téléphone: "221771234567" ou "+221 77 123 45 67"');
  print('- PIN: "1234"');
  print('- Confirmation PIN: "1234"');
  print('');
  print('Ces données devraient maintenant fonctionner sans erreur !');
}
