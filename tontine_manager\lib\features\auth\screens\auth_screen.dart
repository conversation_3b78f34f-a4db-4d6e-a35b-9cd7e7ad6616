import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tontine_manager/features/auth/providers/auth_provider.dart';
import 'package:tontine_manager/core/constants/app_colors.dart';
import 'package:tontine_manager/core/constants/app_strings.dart';
import 'package:tontine_manager/shared/widgets/custom_button.dart';
import 'package:tontine_manager/shared/widgets/custom_text_field.dart';
import 'package:tontine_manager/shared/widgets/loading_overlay.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _nameController = TextEditingController();
  final _pinController = TextEditingController();
  final _confirmPinController = TextEditingController();
  
  bool _isLoginMode = true;
  bool _obscurePin = true;
  bool _obscureConfirmPin = true;

  @override
  void dispose() {
    _phoneController.dispose();
    _nameController.dispose();
    _pinController.dispose();
    _confirmPinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return LoadingOverlay(
            isLoading: authProvider.isLoading,
            child: Container(
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: SafeArea(
                child: Center(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Card(
                      elevation: 8,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildHeader(),
                              const SizedBox(height: 32),
                              _buildPhoneField(),
                              if (!_isLoginMode) ...[
                                const SizedBox(height: 16),
                                _buildNameField(),
                              ],
                              const SizedBox(height: 16),
                              _buildPinField(),
                              if (!_isLoginMode) ...[
                                const SizedBox(height: 16),
                                _buildConfirmPinField(),
                              ],
                              const SizedBox(height: 24),
                              if (authProvider.errorMessage != null)
                                _buildErrorMessage(authProvider.errorMessage!),
                              const SizedBox(height: 16),
                              _buildSubmitButton(),
                              const SizedBox(height: 16),
                              _buildToggleModeButton(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(40),
          ),
          child: const Icon(
            Icons.account_balance,
            size: 40,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          AppStrings.appName,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _isLoginMode ? AppStrings.welcome : 'Créer un compte',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneField() {
    return CustomTextField(
      controller: _phoneController,
      label: AppStrings.phoneNumber,
      hintText: '+221 XX XXX XX XX',
      keyboardType: TextInputType.phone,
      prefixIcon: Icons.phone,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return AppStrings.phoneRequired;
        }
        if (value.length < 9) {
          return 'Numéro de téléphone invalide';
        }
        return null;
      },
    );
  }

  Widget _buildNameField() {
    return CustomTextField(
      controller: _nameController,
      label: AppStrings.fullName,
      hintText: 'Entrez votre nom complet',
      prefixIcon: Icons.person,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return AppStrings.nameRequired;
        }
        if (value.length < 2) {
          return 'Le nom doit contenir au moins 2 caractères';
        }
        return null;
      },
    );
  }

  Widget _buildPinField() {
    return CustomTextField(
      controller: _pinController,
      label: AppStrings.pin,
      hintText: 'Code à 4 chiffres',
      keyboardType: TextInputType.number,
      obscureText: _obscurePin,
      maxLength: 4,
      prefixIcon: Icons.lock,
      suffixIcon: IconButton(
        icon: Icon(_obscurePin ? Icons.visibility : Icons.visibility_off),
        onPressed: () => setState(() => _obscurePin = !_obscurePin),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Code PIN requis';
        }
        if (value.length != 4) {
          return 'Le code PIN doit contenir 4 chiffres';
        }
        return null;
      },
    );
  }

  Widget _buildConfirmPinField() {
    return CustomTextField(
      controller: _confirmPinController,
      label: AppStrings.confirmPin,
      hintText: 'Confirmez votre code PIN',
      keyboardType: TextInputType.number,
      obscureText: _obscureConfirmPin,
      maxLength: 4,
      prefixIcon: Icons.lock_outline,
      suffixIcon: IconButton(
        icon: Icon(_obscureConfirmPin ? Icons.visibility : Icons.visibility_off),
        onPressed: () => setState(() => _obscureConfirmPin = !_obscureConfirmPin),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Confirmation du PIN requise';
        }
        if (value != _pinController.text) {
          return AppStrings.pinMismatch;
        }
        return null;
      },
    );
  }

  Widget _buildErrorMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.error.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: AppColors.error, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(color: AppColors.error, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return CustomButton(
      text: _isLoginMode ? AppStrings.login : AppStrings.register,
      onPressed: _handleSubmit,
      isFullWidth: true,
    );
  }

  Widget _buildToggleModeButton() {
    return TextButton(
      onPressed: () {
        setState(() {
          _isLoginMode = !_isLoginMode;
          _clearForm();
        });
      },
      child: Text(
        _isLoginMode ? AppStrings.noAccount : AppStrings.hasAccount,
        style: TextStyle(color: AppColors.primary),
      ),
    );
  }

  void _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    bool success = false;

    if (_isLoginMode) {
      success = await authProvider.login(
        phoneNumber: _phoneController.text.trim(),
        pin: _pinController.text,
      );
    } else {
      success = await authProvider.register(
        phoneNumber: _phoneController.text.trim(),
        fullName: _nameController.text.trim(),
        pin: _pinController.text,
      );
    }

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isLoginMode ? AppStrings.loginSuccess : AppStrings.registrationSuccess),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  void _clearForm() {
    _phoneController.clear();
    _nameController.clear();
    _pinController.clear();
    _confirmPinController.clear();
  }
}
