enum MemberRole {
  manager,    // Gestionnaire
  collector,  // Collecteur
  secretary,  // <PERSON><PERSON><PERSON><PERSON>
  member,     // Membre standard
}

class Member {
  final String id;
  final String tontineId;
  final String userId;
  final MemberRole role;
  final DateTime joinDate;
  final int? positionInCycle;
  final bool isActive;

  Member({
    required this.id,
    required this.tontineId,
    required this.userId,
    required this.role,
    required this.joinDate,
    this.positionInCycle,
    this.isActive = true,
  });

  // Conversion vers Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tontine_id': tontineId,
      'user_id': userId,
      'role': role.name,
      'join_date': joinDate.toIso8601String(),
      'position_in_cycle': positionInCycle,
      'is_active': isActive ? 1 : 0,
    };
  }

  // Création depuis Map (base de données)
  factory Member.fromMap(Map<String, dynamic> map) {
    return Member(
      id: map['id'] as String,
      tontineId: map['tontine_id'] as String,
      userId: map['user_id'] as String,
      role: MemberRole.values.firstWhere(
        (e) => e.name == map['role'],
        orElse: () => MemberRole.member,
      ),
      joinDate: DateTime.parse(map['join_date'] as String),
      positionInCycle: map['position_in_cycle'] as int?,
      isActive: (map['is_active'] as int) == 1,
    );
  }

  // Nom d'affichage du rôle
  String get roleDisplayName {
    switch (role) {
      case MemberRole.manager:
        return 'Gestionnaire';
      case MemberRole.collector:
        return 'Collecteur';
      case MemberRole.secretary:
        return 'Secrétaire';
      case MemberRole.member:
        return 'Membre';
    }
  }

  // Vérifier si le membre a des privilèges administratifs
  bool get hasAdminPrivileges {
    return role == MemberRole.manager || role == MemberRole.collector;
  }

  // Vérifier si le membre peut collecter les cotisations
  bool get canCollectContributions {
    return role == MemberRole.manager || role == MemberRole.collector;
  }

  // Vérifier si le membre peut gérer les membres
  bool get canManageMembers {
    return role == MemberRole.manager;
  }

  // Copie avec modifications
  Member copyWith({
    String? id,
    String? tontineId,
    String? userId,
    MemberRole? role,
    DateTime? joinDate,
    int? positionInCycle,
    bool? isActive,
  }) {
    return Member(
      id: id ?? this.id,
      tontineId: tontineId ?? this.tontineId,
      userId: userId ?? this.userId,
      role: role ?? this.role,
      joinDate: joinDate ?? this.joinDate,
      positionInCycle: positionInCycle ?? this.positionInCycle,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'Member(id: $id, userId: $userId, role: $role, tontineId: $tontineId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Member && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
