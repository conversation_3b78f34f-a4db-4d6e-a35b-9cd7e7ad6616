name: tontine_manager
description: Application mobile de gestion des tontines sénégalaises
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  
  # Database
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # Local Storage
  shared_preferences: ^2.2.2
  
  # Authentication
  local_auth: ^2.1.6
  crypto: ^3.0.3
  
  # UI Components
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  
  # Utilities
  intl: ^0.18.1
  uuid: ^4.1.0
  
  # Networking (for future phases)
  http: ^1.1.0
  
  # File handling
  path_provider: ^2.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
  
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: fonts/Roboto-Regular.ttf
  #       - asset: fonts/Roboto-Bold.ttf
  #         weight: 700
