import 'package:flutter/material.dart';

class AppColors {
  // Couleurs principales inspirées des couleurs sénégalaises
  static const Color primary = Color(0xFF2E7D32); // Vert sénégalais
  static const Color secondary = Color(0xFFFFC107); // Jaune/Or
  static const Color accent = Color(0xFFE53935); // Rouge
  
  // Couleurs de fond
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Colors.white;
  static const Color cardBackground = Colors.white;
  
  // Couleurs de texte
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textLight = Color(0xFF9E9E9E);
  
  // Couleurs de statut
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Couleurs spécifiques aux tontines
  static const Color tontineActive = Color(0xFF4CAF50);
  static const Color tontineInactive = Color(0xFF9E9E9E);
  static const Color contributionPaid = Color(0xFF4CAF50);
  static const Color contributionPending = Color(0xFFFF9800);
  static const Color contributionLate = Color(0xFFF44336);
  
  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, Color(0xFF1B5E20)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient cardGradient = LinearGradient(
    colors: [Colors.white, Color(0xFFFAFAFA)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
