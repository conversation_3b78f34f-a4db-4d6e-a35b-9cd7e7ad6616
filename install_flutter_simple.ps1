# Script d'installation Flutter simplifie
Write-Host "Installation de Flutter pour Tontine Manager" -ForegroundColor Green

# Creer le dossier de developpement
$devPath = "C:\dev"
if (!(Test-Path $devPath)) {
    Write-Host "Creation du dossier $devPath..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $devPath -Force
}

# URL de telechargement Flutter
$flutterUrl = "https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.16.5-stable.zip"
$flutterZip = "$devPath\flutter.zip"
$flutterPath = "$devPath\flutter"

Write-Host "Telechargement de Flutter..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $flutterUrl -OutFile $flutterZip -UseBasicParsing
    Write-Host "Telechargement termine" -ForegroundColor Green
    
    Write-Host "Extraction de Flutter..." -ForegroundColor Yellow
    Expand-Archive -Path $flutterZip -DestinationPath $devPath -Force
    
    Remove-Item $flutterZip -Force
    Write-Host "Flutter extrait dans $flutterPath" -ForegroundColor Green
} catch {
    Write-Host "Erreur lors du telechargement: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Veuillez telecharger manuellement depuis https://flutter.dev" -ForegroundColor Yellow
    exit 1
}

# Ajouter Flutter au PATH
$flutterBin = "$flutterPath\bin"
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

if ($currentPath -notlike "*$flutterBin*") {
    Write-Host "Ajout de Flutter au PATH..." -ForegroundColor Yellow
    $newPath = "$currentPath;$flutterBin"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    Write-Host "Flutter ajoute au PATH" -ForegroundColor Green
    Write-Host "Redemarrez votre terminal pour que les changements prennent effet" -ForegroundColor Yellow
} else {
    Write-Host "Flutter deja dans le PATH" -ForegroundColor Green
}

Write-Host "Installation terminee !" -ForegroundColor Green
Write-Host "Prochaines etapes :" -ForegroundColor Cyan
Write-Host "1. Redemarrez votre terminal PowerShell" -ForegroundColor White
Write-Host "2. Executez: flutter doctor" -ForegroundColor White
Write-Host "3. Naviguez vers le projet: cd tontine_manager" -ForegroundColor White
Write-Host "4. Installez les dependances: flutter pub get" -ForegroundColor White
Write-Host "5. Lancez l'app: flutter run" -ForegroundColor White
