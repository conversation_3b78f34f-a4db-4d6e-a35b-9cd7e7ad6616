enum TontineType {
  natt,
  mbotay,
  classic,
  family,
  commercial,
}

enum TontineFrequency {
  weekly,
  biweekly,
  monthly,
}

enum TontineStatus {
  active,
  completed,
  paused,
  cancelled,
}

class Tontine {
  final String id;
  final String name;
  final TontineType type;
  final String creatorId;
  final double contributionAmount;
  final TontineFrequency frequency;
  final DateTime startDate;
  final DateTime? endDate;
  final int maxMembers;
  final TontineStatus status;
  final DateTime createdAt;
  final Map<String, dynamic>? rules; // Règles spécifiques au type

  Tontine({
    required this.id,
    required this.name,
    required this.type,
    required this.creatorId,
    required this.contributionAmount,
    required this.frequency,
    required this.startDate,
    this.endDate,
    required this.maxMembers,
    this.status = TontineStatus.active,
    required this.createdAt,
    this.rules,
  });

  // Conversion vers Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'creator_id': creatorId,
      'contribution_amount': contributionAmount,
      'frequency': frequency.name,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'max_members': maxMembers,
      'status': status.name,
      'created_at': createdAt.toIso8601String(),
      'rules': rules != null ? rules.toString() : null,
    };
  }

  // Création depuis Map (base de données)
  factory Tontine.fromMap(Map<String, dynamic> map) {
    return Tontine(
      id: map['id'] as String,
      name: map['name'] as String,
      type: TontineType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TontineType.classic,
      ),
      creatorId: map['creator_id'] as String,
      contributionAmount: (map['contribution_amount'] as num).toDouble(),
      frequency: TontineFrequency.values.firstWhere(
        (e) => e.name == map['frequency'],
        orElse: () => TontineFrequency.monthly,
      ),
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: map['end_date'] != null 
          ? DateTime.parse(map['end_date'] as String)
          : null,
      maxMembers: map['max_members'] as int,
      status: TontineStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => TontineStatus.active,
      ),
      createdAt: DateTime.parse(map['created_at'] as String),
      rules: map['rules'] != null ? {} : null, // À implémenter selon les besoins
    );
  }

  // Méthodes utilitaires
  String get typeDisplayName {
    switch (type) {
      case TontineType.natt:
        return 'Natt';
      case TontineType.mbotay:
        return 'Mbotay';
      case TontineType.classic:
        return 'Tontine Classique';
      case TontineType.family:
        return 'Tontine Familiale';
      case TontineType.commercial:
        return 'Tontine Commerciale';
    }
  }

  String get frequencyDisplayName {
    switch (frequency) {
      case TontineFrequency.weekly:
        return 'Hebdomadaire';
      case TontineFrequency.biweekly:
        return 'Bi-hebdomadaire';
      case TontineFrequency.monthly:
        return 'Mensuel';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case TontineStatus.active:
        return 'Active';
      case TontineStatus.completed:
        return 'Terminée';
      case TontineStatus.paused:
        return 'En pause';
      case TontineStatus.cancelled:
        return 'Annulée';
    }
  }

  // Copie avec modifications
  Tontine copyWith({
    String? id,
    String? name,
    TontineType? type,
    String? creatorId,
    double? contributionAmount,
    TontineFrequency? frequency,
    DateTime? startDate,
    DateTime? endDate,
    int? maxMembers,
    TontineStatus? status,
    DateTime? createdAt,
    Map<String, dynamic>? rules,
  }) {
    return Tontine(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      creatorId: creatorId ?? this.creatorId,
      contributionAmount: contributionAmount ?? this.contributionAmount,
      frequency: frequency ?? this.frequency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      maxMembers: maxMembers ?? this.maxMembers,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      rules: rules ?? this.rules,
    );
  }

  @override
  String toString() {
    return 'Tontine(id: $id, name: $name, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Tontine && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
