import 'package:sqflite/sqflite.dart';
import 'package:tontine_manager/core/database/database_helper.dart';
import 'package:tontine_manager/shared/models/tontine.dart';
import 'package:tontine_manager/shared/models/member.dart';

class TontineService {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;

  // Créer une nouvelle tontine
  Future<bool> createTontine(Tontine tontine) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.insert(
        'tontines',
        tontine.toMap(),
        conflictAlgorithm: ConflictAlgorithm.abort,
      );
      return result > 0;
    } catch (e) {
      print('Erreur lors de la création de la tontine: $e');
      return false;
    }
  }

  // Récupérer une tontine par son ID
  Future<Tontine?> getTontineById(String id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tontines',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return Tontine.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      print('Erreur lors de la récupération de la tontine: $e');
      return null;
    }
  }

  // Récupérer toutes les tontines d'un utilisateur
  Future<List<Tontine>> getUserTontines(String userId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT DISTINCT t.* FROM tontines t
        INNER JOIN tontine_members tm ON t.id = tm.tontine_id
        WHERE tm.user_id = ? AND tm.is_active = 1
        ORDER BY t.created_at DESC
      ''', [userId]);

      return List.generate(maps.length, (i) {
        return Tontine.fromMap(maps[i]);
      });
    } catch (e) {
      print('Erreur lors de la récupération des tontines de l\'utilisateur: $e');
      return [];
    }
  }

  // Mettre à jour une tontine
  Future<bool> updateTontine(Tontine tontine) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.update(
        'tontines',
        tontine.toMap(),
        where: 'id = ?',
        whereArgs: [tontine.id],
      );
      return result > 0;
    } catch (e) {
      print('Erreur lors de la mise à jour de la tontine: $e');
      return false;
    }
  }

  // Supprimer une tontine
  Future<bool> deleteTontine(String id) async {
    try {
      final db = await _dbHelper.database;
      
      // Supprimer d'abord les membres
      await db.delete(
        'tontine_members',
        where: 'tontine_id = ?',
        whereArgs: [id],
      );
      
      // Supprimer les cotisations
      await db.delete(
        'contributions',
        where: 'tontine_id = ?',
        whereArgs: [id],
      );
      
      // Supprimer les cycles de distribution
      await db.delete(
        'distribution_cycles',
        where: 'tontine_id = ?',
        whereArgs: [id],
      );
      
      // Supprimer la tontine
      final result = await db.delete(
        'tontines',
        where: 'id = ?',
        whereArgs: [id],
      );
      
      return result > 0;
    } catch (e) {
      print('Erreur lors de la suppression de la tontine: $e');
      return false;
    }
  }

  // Ajouter un membre à une tontine
  Future<bool> addMember(Member member) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.insert(
        'tontine_members',
        member.toMap(),
        conflictAlgorithm: ConflictAlgorithm.abort,
      );
      return result > 0;
    } catch (e) {
      print('Erreur lors de l\'ajout du membre: $e');
      return false;
    }
  }

  // Récupérer les membres d'une tontine
  Future<List<Member>> getTontineMembers(String tontineId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tontine_members',
        where: 'tontine_id = ? AND is_active = 1',
        whereArgs: [tontineId],
        orderBy: 'position_in_cycle ASC',
      );

      return List.generate(maps.length, (i) {
        return Member.fromMap(maps[i]);
      });
    } catch (e) {
      print('Erreur lors de la récupération des membres: $e');
      return [];
    }
  }

  // Récupérer un membre spécifique
  Future<Member?> getMemberByUserAndTontine(String userId, String tontineId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tontine_members',
        where: 'user_id = ? AND tontine_id = ? AND is_active = 1',
        whereArgs: [userId, tontineId],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return Member.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      print('Erreur lors de la récupération du membre: $e');
      return null;
    }
  }

  // Mettre à jour un membre
  Future<bool> updateMember(Member member) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.update(
        'tontine_members',
        member.toMap(),
        where: 'id = ?',
        whereArgs: [member.id],
      );
      return result > 0;
    } catch (e) {
      print('Erreur lors de la mise à jour du membre: $e');
      return false;
    }
  }

  // Supprimer un membre (désactiver)
  Future<bool> removeMember(String memberId) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.update(
        'tontine_members',
        {'is_active': 0},
        where: 'id = ?',
        whereArgs: [memberId],
      );
      return result > 0;
    } catch (e) {
      print('Erreur lors de la suppression du membre: $e');
      return false;
    }
  }

  // Récupérer les tontines par statut
  Future<List<Tontine>> getTontinesByStatus(TontineStatus status) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tontines',
        where: 'status = ?',
        whereArgs: [status.name],
        orderBy: 'created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return Tontine.fromMap(maps[i]);
      });
    } catch (e) {
      print('Erreur lors de la récupération des tontines par statut: $e');
      return [];
    }
  }

  // Rechercher des tontines par nom
  Future<List<Tontine>> searchTontinesByName(String name) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'tontines',
        where: 'name LIKE ?',
        whereArgs: ['%$name%'],
        orderBy: 'created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return Tontine.fromMap(maps[i]);
      });
    } catch (e) {
      print('Erreur lors de la recherche de tontines: $e');
      return [];
    }
  }
}
