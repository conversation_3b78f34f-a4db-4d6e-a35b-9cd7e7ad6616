import 'package:flutter/material.dart';
import 'package:tontine_manager/core/utils/debug_helper.dart';
import 'package:tontine_manager/core/constants/app_colors.dart';
import 'package:tontine_manager/shared/widgets/custom_button.dart';
import 'package:tontine_manager/shared/widgets/custom_text_field.dart';

class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  final _phoneController = TextEditingController(text: '221771234567');
  final _nameController = TextEditingController(text: '<PERSON>ou Diop <PERSON>rr');
  final _pinController = TextEditingController(text: '1234');
  
  Map<String, dynamic>? _lastDiagnosis;
  bool _isRunning = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _nameController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔧 Diagnostic Tontine Manager'),
        backgroundColor: AppColors.warning,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 24),
            _buildTestDataSection(),
            const SizedBox(height: 24),
            _buildActionsSection(),
            const SizedBox(height: 24),
            if (_lastDiagnosis != null) _buildResultsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: AppColors.info.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.info),
                const SizedBox(width: 8),
                Text(
                  'Outil de Diagnostic',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.info,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Cet outil permet de diagnostiquer les problèmes de création de compte '
              'en testant chaque étape du processus d\'inscription.',
            ),
            const SizedBox(height: 8),
            const Text(
              '⚠️ Utilisez uniquement en mode développement.',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.warning,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestDataSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📋 Données de Test',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _phoneController,
              label: 'Numéro de téléphone',
              prefixIcon: Icons.phone,
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _nameController,
              label: 'Nom complet',
              prefixIcon: Icons.person,
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _pinController,
              label: 'Code PIN',
              prefixIcon: Icons.lock,
              keyboardType: TextInputType.number,
              maxLength: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🔧 Actions de Diagnostic',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Diagnostiquer',
                    icon: Icons.bug_report,
                    onPressed: _isRunning ? null : _runDiagnosis,
                    isLoading: _isRunning,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: 'Nettoyer',
                    icon: Icons.cleaning_services,
                    type: ButtonType.secondary,
                    onPressed: _isRunning ? null : _cleanupTestData,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            CustomButton(
              text: 'Tester Inscription Complète',
              icon: Icons.person_add,
              type: ButtonType.outline,
              onPressed: _isRunning ? null : _testFullRegistration,
              isFullWidth: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    final diagnosis = _lastDiagnosis!;
    final isSuccess = diagnosis['success'] as bool;
    final errors = diagnosis['errors'] as List<String>;
    final warnings = diagnosis['warnings'] as List<String>;
    final checks = diagnosis['checks'] as Map<String, dynamic>;

    return Card(
      color: isSuccess 
          ? AppColors.success.withOpacity(0.1)
          : AppColors.error.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isSuccess ? Icons.check_circle : Icons.error,
                  color: isSuccess ? AppColors.success : AppColors.error,
                ),
                const SizedBox(width: 8),
                Text(
                  isSuccess ? '✅ Diagnostic Réussi' : '❌ Problèmes Détectés',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isSuccess ? AppColors.success : AppColors.error,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Vérifications
            Text(
              '🔍 Vérifications:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...checks.entries.map((entry) {
              final status = entry.value == true ? '✅' : 
                           entry.value == false ? '❌' : '⚠️';
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Text('$status ${entry.key}: ${entry.value}'),
              );
            }),
            
            // Erreurs
            if (errors.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                '❌ Erreurs:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.error,
                ),
              ),
              const SizedBox(height: 8),
              ...errors.map((error) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Text(
                  '• $error',
                  style: TextStyle(color: AppColors.error),
                ),
              )),
            ],
            
            // Avertissements
            if (warnings.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                '⚠️ Avertissements:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.warning,
                ),
              ),
              const SizedBox(height: 8),
              ...warnings.map((warning) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Text(
                  '• $warning',
                  style: TextStyle(color: AppColors.warning),
                ),
              )),
            ],
            
            const SizedBox(height: 16),
            CustomButton(
              text: 'Copier Rapport Complet',
              icon: Icons.copy,
              type: ButtonType.text,
              onPressed: () => _copyFullReport(diagnosis),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runDiagnosis() async {
    setState(() {
      _isRunning = true;
      _lastDiagnosis = null;
    });

    try {
      final diagnosis = await DebugHelper.diagnoseRegistration(
        phoneNumber: _phoneController.text,
        fullName: _nameController.text,
        pin: _pinController.text,
      );

      setState(() {
        _lastDiagnosis = diagnosis;
      });

      // Afficher un snackbar avec le résultat
      if (mounted) {
        final isSuccess = diagnosis['success'] as bool;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isSuccess 
                  ? 'Diagnostic terminé: Aucun problème détecté'
                  : 'Diagnostic terminé: Problèmes détectés',
            ),
            backgroundColor: isSuccess ? AppColors.success : AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du diagnostic: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _cleanupTestData() async {
    try {
      await DebugHelper.cleanupTestData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Données de test nettoyées'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du nettoyage: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _testFullRegistration() async {
    // Cette méthode testerait le processus complet d'inscription
    // en utilisant le AuthProvider
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Test d\'inscription complète - À implémenter'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _copyFullReport(Map<String, dynamic> diagnosis) {
    final report = DebugHelper.formatDiagnosis(diagnosis);
    // En production, utiliser Clipboard.setData
    print(report);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Rapport copié dans la console'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
