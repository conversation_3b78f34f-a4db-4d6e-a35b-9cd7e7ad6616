import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:tontine_manager/features/auth/providers/auth_provider.dart';
import 'package:tontine_manager/features/tontines/providers/tontine_provider.dart';
import 'package:tontine_manager/features/tontines/screens/create_tontine_screen.dart';
import 'package:tontine_manager/core/constants/app_colors.dart';
import 'package:tontine_manager/core/constants/app_strings.dart';
import 'package:tontine_manager/shared/widgets/custom_button.dart';
import 'package:tontine_manager/shared/widgets/tontine_card.dart';
import 'package:tontine_manager/shared/widgets/stats_card.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserData();
    });
  }

  void _loadUserData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final tontineProvider = Provider.of<TontineProvider>(context, listen: false);
    
    if (authProvider.currentUser != null) {
      tontineProvider.loadUserTontines(authProvider.currentUser!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Consumer2<AuthProvider, TontineProvider>(
        builder: (context, authProvider, tontineProvider, child) {
          if (tontineProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async => _loadUserData(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildGreeting(authProvider.currentUser?.fullName ?? ''),
                  const SizedBox(height: 24),
                  _buildStatsSection(tontineProvider, authProvider.currentUser?.id ?? ''),
                  const SizedBox(height: 24),
                  _buildTontinesSection(tontineProvider),
                  const SizedBox(height: 24),
                  _buildCreateTontineButton(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(AppStrings.dashboard),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () {
            // TODO: Implémenter les notifications
          },
        ),
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'profile':
                // TODO: Naviguer vers le profil
                break;
              case 'settings':
                // TODO: Naviguer vers les paramètres
                break;
              case 'logout':
                _handleLogout();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: ListTile(
                leading: Icon(Icons.person_outline),
                title: Text(AppStrings.profile),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings_outlined),
                title: Text(AppStrings.settings),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: ListTile(
                leading: Icon(Icons.logout, color: AppColors.error),
                title: Text(AppStrings.logout, style: TextStyle(color: AppColors.error)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGreeting(String userName) {
    final hour = DateTime.now().hour;
    String greeting;
    
    if (hour < 12) {
      greeting = AppStrings.greetingMorning;
    } else if (hour < 18) {
      greeting = AppStrings.greetingAfternoon;
    } else {
      greeting = AppStrings.greetingEvening;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$greeting ${userName.split(' ').first}! 👋',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  DateFormat('EEEE d MMMM yyyy', 'fr_FR').format(DateTime.now()),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.account_balance,
              color: Colors.white,
              size: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection(TontineProvider tontineProvider, String userId) {
    final stats = tontineProvider.getUserStats(userId);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.monthlyStats,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: StatsCard(
                title: AppStrings.myTontines,
                value: '${stats['activeTontines']}',
                icon: Icons.group,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatsCard(
                title: AppStrings.totalContributed,
                value: '${NumberFormat('#,###').format(stats['totalContributed'])} ${AppStrings.fcfa}',
                icon: Icons.trending_up,
                color: AppColors.success,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTontinesSection(TontineProvider tontineProvider) {
    final activeTontines = tontineProvider.getActiveTontines();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppStrings.myTontines,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            if (activeTontines.isNotEmpty)
              TextButton(
                onPressed: () {
                  // TODO: Naviguer vers la liste complète
                },
                child: const Text('Voir tout'),
              ),
          ],
        ),
        const SizedBox(height: 16),
        if (activeTontines.isEmpty)
          _buildEmptyTontinesState()
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: activeTontines.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              return TontineCard(
                tontine: activeTontines[index],
                onTap: () => _handleTontineTap(activeTontines[index].id),
              );
            },
          ),
      ],
    );
  }

  Widget _buildEmptyTontinesState() {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.textLight.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.account_balance_outlined,
            size: 64,
            color: AppColors.textLight,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucune tontine',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Créez votre première tontine pour commencer',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textLight,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCreateTontineButton() {
    return CustomButton(
      text: AppStrings.createTontine,
      icon: Icons.add,
      onPressed: _handleCreateTontine,
      isFullWidth: true,
    );
  }

  void _handleTontineTap(String tontineId) {
    final tontineProvider = Provider.of<TontineProvider>(context, listen: false);
    tontineProvider.selectTontine(tontineId);
    
    // TODO: Naviguer vers les détails de la tontine
  }

  void _handleCreateTontine() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateTontineScreen(),
      ),
    );
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Déconnexion'),
        content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(AppStrings.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<AuthProvider>(context, listen: false).logout();
            },
            child: const Text(
              AppStrings.logout,
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
