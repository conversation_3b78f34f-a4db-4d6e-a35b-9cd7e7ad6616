# 🔧 Guide de Dépannage - Problèmes de Création de Compte

## 📋 Informations à Collecter

### 1. Message d'Erreur Exact
**Copie exactement le message d'erreur qui s'affiche :**
```
[Coller ici le message d'erreur exact]
```

### 2. Données Saisies
**Quelles données as-tu saisies dans les champs ?**
- **Nom complet** : ________________
- **Numéro de téléphone** : ________________
- **Code PIN** : ________________ (nombre de chiffres)
- **Confirmation PIN** : ________________

### 3. Comportement Observé
**Coche ce qui correspond à ton expérience :**
- [ ] L'erreur apparaît immédiatement après avoir cliqué sur "S'inscrire"
- [ ] L'erreur apparaît après quelques secondes de chargement
- [ ] L'application se fige/plante complètement
- [ ] L'application reste fonctionnelle mais affiche une erreur
- [ ] L'écran reste sur l'inscription sans message d'erreur
- [ ] Autre : ________________

### 4. Plateforme de Test
**Sur quelle plateforme testes-tu ?**
- [ ] Windows Desktop (flutter run -d windows)
- [ ] Navigateur Web Chrome (flutter run -d chrome)
- [ ] Navigateur Web Edge
- [ ] Émulateur Android
- [ ] Appareil Android physique
- [ ] Autre : ________________

## 🔍 Causes Possibles Identifiées

### A. Problèmes de Validation des Données

#### 1. Numéro de Téléphone
**Formats acceptés :**
- ✅ `221771234567` (format international sans +)
- ✅ `+221771234567` (format international avec +)
- ✅ `77 123 45 67` (format local avec espaces)
- ❌ `123` (trop court)
- ❌ `abcd` (non numérique)

#### 2. Code PIN
**Règles :**
- ✅ Exactement 4 chiffres : `1234`
- ❌ Moins de 4 chiffres : `12`
- ❌ Plus de 4 chiffres : `12345`
- ❌ Lettres : `abcd`

#### 3. Nom Complet
**Règles :**
- ✅ Au moins 2 caractères
- ✅ Lettres et espaces autorisés
- ❌ Champ vide

### B. Problèmes de Base de Données

#### 1. Initialisation SQLite
**Symptômes :**
- Message d'erreur mentionnant "database"
- Erreur "table doesn't exist"
- Erreur "no such column"

**Solutions :**
1. Redémarrer l'application
2. Supprimer les données de l'app (cache)
3. Utiliser l'outil de diagnostic intégré

#### 2. Contraintes d'Unicité
**Symptôme :**
- Erreur "UNIQUE constraint failed"
- Message "Un compte existe déjà"

**Solution :**
- Utiliser un numéro de téléphone différent
- Ou nettoyer les données de test

### C. Problèmes de Permissions

#### 1. Permissions de Stockage
**Symptômes :**
- Erreur d'accès aux fichiers
- Impossible de créer la base de données

**Solutions :**
- Vérifier les permissions de l'application
- Tester sur une autre plateforme

## 🛠️ Outils de Diagnostic Intégrés

### Accès aux Outils de Debug
1. **En mode développement**, un bouton "Outils de Diagnostic" apparaît en bas de l'écran d'authentification
2. Cliquer sur ce bouton pour accéder aux outils de diagnostic
3. Utiliser les fonctions suivantes :

#### A. Diagnostic Automatique
- Remplir les champs de test avec tes données
- Cliquer sur "Diagnostiquer"
- Analyser le rapport généré

#### B. Nettoyage des Données
- Cliquer sur "Nettoyer" pour supprimer les données de test
- Réessayer la création de compte

#### C. Test Complet
- Utiliser "Tester Inscription Complète" pour un test automatisé

## 🔧 Solutions par Type d'Erreur

### Erreur : "Numéro de téléphone requis"
**Cause :** Champ vide ou validation échouée
**Solution :**
1. Vérifier que le champ n'est pas vide
2. Utiliser un format valide (ex: 221771234567)

### Erreur : "Code PIN requis" ou "4 chiffres"
**Cause :** PIN invalide
**Solution :**
1. Saisir exactement 4 chiffres
2. Vérifier que la confirmation correspond

### Erreur : "Un compte existe déjà"
**Cause :** Numéro déjà utilisé
**Solution :**
1. Utiliser un autre numéro
2. Ou nettoyer les données de test

### Erreur : "Erreur de base de données"
**Cause :** Problème SQLite
**Solution :**
1. Redémarrer l'application
2. Utiliser l'outil de diagnostic
3. Vérifier les logs de la console

### Erreur : "Erreur lors de la création"
**Cause :** Problème général
**Solution :**
1. Utiliser l'outil de diagnostic complet
2. Vérifier tous les champs
3. Redémarrer l'application

## 📊 Collecte de Logs

### Console Flutter
**Pour collecter les logs :**
1. Ouvrir la console où `flutter run` s'exécute
2. Reproduire l'erreur
3. Copier les messages d'erreur qui apparaissent

### Logs de Debug
**Dans l'outil de diagnostic :**
1. Exécuter le diagnostic
2. Copier le rapport complet
3. Analyser les sections "Erreurs" et "Avertissements"

## 🚀 Tests de Validation

### Données de Test Recommandées
```
Nom complet: Fatou Diop Sarr
Téléphone: 221771234567
PIN: 1234
Confirmation: 1234
```

### Scénarios de Test
1. **Test normal** : Données valides ci-dessus
2. **Test numéro court** : Téléphone = "123"
3. **Test PIN court** : PIN = "12"
4. **Test nom vide** : Nom = ""
5. **Test confirmation différente** : Confirmation = "5678"

## 📞 Prochaines Étapes

### Si le Problème Persiste
1. **Utiliser l'outil de diagnostic intégré**
2. **Collecter le rapport complet**
3. **Tester sur une autre plateforme**
4. **Fournir les informations collectées**

### Informations à Fournir
- Message d'erreur exact
- Données saisies (sans le PIN réel)
- Rapport de diagnostic complet
- Plateforme de test
- Logs de la console Flutter

---

**Note :** Ce guide est conçu pour t'aider à identifier et résoudre rapidement les problèmes de création de compte. Les outils de diagnostic intégrés fourniront des informations détaillées pour un dépannage précis.
