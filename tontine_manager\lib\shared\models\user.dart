class User {
  final String id;
  final String phoneNumber;
  final String fullName;
  final String? profilePhoto;
  final String pinHash;
  final bool biometricEnabled;
  final DateTime createdAt;
  final DateTime? lastActive;

  User({
    required this.id,
    required this.phoneNumber,
    required this.fullName,
    this.profilePhoto,
    required this.pinHash,
    this.biometricEnabled = false,
    required this.createdAt,
    this.lastActive,
  });

  // Conversion vers Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'phone_number': phoneNumber,
      'full_name': fullName,
      'profile_photo': profilePhoto,
      'pin_hash': pinHash,
      'biometric_enabled': biometricEnabled ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'last_active': lastActive?.toIso8601String(),
    };
  }

  // Création depuis Map (base de données)
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] as String,
      phoneNumber: map['phone_number'] as String,
      fullName: map['full_name'] as String,
      profilePhoto: map['profile_photo'] as String?,
      pinHash: map['pin_hash'] as String,
      biometricEnabled: (map['biometric_enabled'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      lastActive: map['last_active'] != null 
          ? DateTime.parse(map['last_active'] as String)
          : null,
    );
  }

  // Copie avec modifications
  User copyWith({
    String? id,
    String? phoneNumber,
    String? fullName,
    String? profilePhoto,
    String? pinHash,
    bool? biometricEnabled,
    DateTime? createdAt,
    DateTime? lastActive,
  }) {
    return User(
      id: id ?? this.id,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      fullName: fullName ?? this.fullName,
      profilePhoto: profilePhoto ?? this.profilePhoto,
      pinHash: pinHash ?? this.pinHash,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      createdAt: createdAt ?? this.createdAt,
      lastActive: lastActive ?? this.lastActive,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, phoneNumber: $phoneNumber, fullName: $fullName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
